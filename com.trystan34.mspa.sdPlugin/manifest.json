{"Name": "MSpa Control", "Version": "*******", "Author": "Trystan34", "Actions": [{"Name": "MSpa Control", "UUID": "com.trystan34.mspa.control", "Icon": "imgs/actions/config/icon", "Tooltip": "Control your MSpa hot tub - displays temperature and other stats.", "PropertyInspectorPath": "ui/mspa-control.html", "UserTitleEnabled": false, "Controllers": ["Keypad"], "States": [{"Image": "imgs/actions/config/key", "TitleAlignment": "middle"}]}], "Category": "MSpa", "CategoryIcon": "imgs/plugin/category-icon", "CodePath": "bin/plugin.js", "Description": "Control your MSpa hot tub directly from Stream Deck", "Icon": "imgs/plugin/marketplace", "SDKVersion": 2, "Software": {"MinimumVersion": "6.5"}, "OS": [{"Platform": "mac", "MinimumVersion": "12"}, {"Platform": "windows", "MinimumVersion": "10"}], "Nodejs": {"Version": "20", "Debug": "enabled"}, "UUID": "com.trystan34.mspa"}