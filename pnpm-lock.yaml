lockfileVersion: "9.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      "@elgato/streamdeck":
        specifier: ^1.0.0
        version: 1.3.1
    devDependencies:
      "@elgato/cli":
        specifier: ^1.4.0
        version: 1.4.0(@types/node@20.15.0)
      "@elgato/eslint-config":
        specifier: ^0.1.0
        version: 0.1.0(@eslint/js@9.30.0)(eslint-plugin-jsdoc@50.8.0(eslint@9.30.0))(eslint@9.30.0)(typescript-eslint@8.35.0(eslint@9.30.0)(typescript@5.8.3))(typescript@5.8.3)
      "@elgato/prettier-config":
        specifier: ^0.2.6
        version: 0.2.6(@trivago/prettier-plugin-sort-imports@5.2.2(prettier@3.6.2))(prettier-plugin-multiline-arrays@4.0.3(prettier@3.6.2))(prettier@3.6.2)
      "@rollup/plugin-commonjs":
        specifier: ^28.0.0
        version: 28.0.6(rollup@4.44.1)
      "@rollup/plugin-node-resolve":
        specifier: ^15.2.2
        version: 15.3.1(rollup@4.44.1)
      "@rollup/plugin-terser":
        specifier: ^0.4.4
        version: 0.4.4(rollup@4.44.1)
      "@rollup/plugin-typescript":
        specifier: ^12.1.0
        version: 12.1.4(rollup@4.44.1)(tslib@2.8.1)(typescript@5.8.3)
      "@trivago/prettier-plugin-sort-imports":
        specifier: ^5.2.2
        version: 5.2.2(prettier@3.6.2)
      "@tsconfig/node20":
        specifier: ^20.1.2
        version: 20.1.6
      "@types/node":
        specifier: ~20.15.0
        version: 20.15.0
      prettier-plugin-multiline-arrays:
        specifier: ^4.0.3
        version: 4.0.3(prettier@3.6.2)
      rollup:
        specifier: ^4.0.2
        version: 4.44.1
      tslib:
        specifier: ^2.6.2
        version: 2.8.1
      typescript:
        specifier: ^5.2.2
        version: 5.8.3

packages:
  "@augment-vir/assert@31.26.0":
    resolution:
      { integrity: sha512-vi32iLl1zXBkd+GfG+KMkinzCEaF8bGs9GyRhAw8P062J58ZgGLq0UeKkk71pA04zw2PD3ct9qRagdk3TQv/Hw== }
    engines: { node: ">=22" }

  "@augment-vir/common@31.26.0":
    resolution:
      { integrity: sha512-3SFDD3eyAN1tdmtWpliWSP2DsINhQPIl+FktVEOrrx6DVxGkikyTiI8OfT23Fr9ORUUajW3Pr4d7TqPlqCY3bw== }
    engines: { node: ">=22" }

  "@augment-vir/core@31.26.0":
    resolution:
      { integrity: sha512-WLuSQ7GRoSiPJ2D1wjdvbR6NINs3lLrQ1l96YqopSB4C6oO4nAtNaG46upbcNn9kVoShpDljFuO1HYbh9Ayl0w== }
    engines: { node: ">=22" }

  "@babel/code-frame@7.27.1":
    resolution:
      { integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg== }
    engines: { node: ">=6.9.0" }

  "@babel/generator@7.27.5":
    resolution:
      { integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw== }
    engines: { node: ">=6.9.0" }

  "@babel/helper-string-parser@7.27.1":
    resolution:
      { integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA== }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-identifier@7.27.1":
    resolution:
      { integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow== }
    engines: { node: ">=6.9.0" }

  "@babel/parser@7.27.7":
    resolution:
      { integrity: sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q== }
    engines: { node: ">=6.0.0" }
    hasBin: true

  "@babel/template@7.27.2":
    resolution:
      { integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw== }
    engines: { node: ">=6.9.0" }

  "@babel/traverse@7.27.7":
    resolution:
      { integrity: sha512-X6ZlfR/O/s5EQ/SnUSLzr+6kGnkg8HXGMzpgsMsrJVcfDtH1vIp6ctCN4eZ1LS5c0+te5Cb6Y514fASjMRJ1nw== }
    engines: { node: ">=6.9.0" }

  "@babel/types@7.27.7":
    resolution:
      { integrity: sha512-8OLQgDScAOHXnAz2cV+RfzzNMipuLVBz2biuAJFMV9bfkNf393je3VM8CLkjQodW5+iWsSJdSgSWT6rsZoXHPw== }
    engines: { node: ">=6.9.0" }

  "@date-vir/duration@7.3.2":
    resolution:
      { integrity: sha512-F/RvIR2tT7wTsoEM6OQXpZV18KHwPpEhTLFw6lNe3cFRZ7dj5skzQGR9U6y5icQZA50ND762plMk3tk8aBMZAg== }
    engines: { node: ">=22" }

  "@elgato/cli@1.4.0":
    resolution:
      { integrity: sha512-xMYe9kf3xzsggtBrRUIeAg+uqMmRx8QemAQ6AMX2cPQnvTJ30mZYGs/ZDBQnmuEZU18HovvyiGGZGp6S/bzigg== }
    engines: { node: ">=20.1.0" }
    hasBin: true

  "@elgato/eslint-config@0.1.0":
    resolution:
      { integrity: sha512-BKLdQpJogXgz9NJ1UkoycUfa67Y8af/6Z0xQJ/gNTf3G6s78mUSyJWdZj5BakPyqxiFZkBMTlQxFAuaVIaniMQ== }
    peerDependencies:
      "@eslint/js": ^9.26.0
      eslint: ^9.26.0
      eslint-plugin-jsdoc: ^50.6.14
      typescript: ^5.8.3
      typescript-eslint: ^8.32.0

  "@elgato/prettier-config@0.2.6":
    resolution:
      { integrity: sha512-+UwI8tqf8i5Qkfzi7UfhgQu4K15DjeT6SrVrTnW3IifmlA+cILE2kqExoo9Sp4Wz1yv9Iq2eSURAyzW51CljjQ== }
    peerDependencies:
      "@trivago/prettier-plugin-sort-imports": ^5.2.1
      prettier: ^3.4.2
      prettier-plugin-multiline-arrays: ^3.0.6

  "@elgato/schemas@0.4.6":
    resolution:
      { integrity: sha512-R9RzTfnWS4zXNepcDr1/ibLIXAuoX5E+aXtThLtKzDHv5bRmTWXFpxPESH0/GaPGw8B2nt7TQD/hTYTYfrw07Q== }

  "@elgato/streamdeck@1.3.1":
    resolution:
      { integrity: sha512-P/KJI+q6fi6PwZCZGyvfDCxZl1QviGBP5uiX+xNnjO6bAtl+Sz8i4QEt5TKyZZKo3Y9rLP2GfUAcYVxoMc38bw== }
    engines: { node: ">=20.5.1" }

  "@es-joy/jsdoccomment@0.50.2":
    resolution:
      { integrity: sha512-YAdE/IJSpwbOTiaURNCKECdAwqrJuFiZhylmesBcIRawtYKnBR2wxPhoIewMg+Yu+QuYvHfJNReWpoxGBKOChA== }
    engines: { node: ">=18" }

  "@eslint-community/eslint-utils@4.7.0":
    resolution:
      { integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw== }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  "@eslint-community/regexpp@4.12.1":
    resolution:
      { integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ== }
    engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

  "@eslint/config-array@0.21.0":
    resolution:
      { integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/config-helpers@0.3.0":
    resolution:
      { integrity: sha512-ViuymvFmcJi04qdZeDc2whTHryouGcDlaxPqarTD0ZE10ISpxGUVZGZDx4w01upyIynL3iu6IXH2bS1NhclQMw== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/core@0.14.0":
    resolution:
      { integrity: sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/core@0.15.1":
    resolution:
      { integrity: sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/eslintrc@3.3.1":
    resolution:
      { integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/js@9.30.0":
    resolution:
      { integrity: sha512-Wzw3wQwPvc9sHM+NjakWTcPx11mbZyiYHuwWa/QfZ7cIRX7WK54PSk7bdyXDaoaopUcMatv1zaQvOAAO8hCdww== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/object-schema@2.1.6":
    resolution:
      { integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@eslint/plugin-kit@0.3.3":
    resolution:
      { integrity: sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@humanfs/core@0.19.1":
    resolution:
      { integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA== }
    engines: { node: ">=18.18.0" }

  "@humanfs/node@0.16.6":
    resolution:
      { integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw== }
    engines: { node: ">=18.18.0" }

  "@humanwhocodes/module-importer@1.0.1":
    resolution:
      { integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA== }
    engines: { node: ">=12.22" }

  "@humanwhocodes/momoa@3.3.8":
    resolution:
      { integrity: sha512-/3PZzor2imi/RLLcnHztkwA79txiVvW145Ve2cp5dxRcH5qOUNJPToasqLFHniTfw4B4lT7jGDdBOPXbXYlIMQ== }
    engines: { node: ">=18" }

  "@humanwhocodes/retry@0.3.1":
    resolution:
      { integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA== }
    engines: { node: ">=18.18" }

  "@humanwhocodes/retry@0.4.3":
    resolution:
      { integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ== }
    engines: { node: ">=18.18" }

  "@inquirer/checkbox@4.1.8":
    resolution:
      { integrity: sha512-d/QAsnwuHX2OPolxvYcgSj7A9DO9H6gVOy2DvBTx+P2LH2iRTo/RSGV3iwCzW024nP9hw98KIuDmdyhZQj1UQg== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/confirm@5.1.12":
    resolution:
      { integrity: sha512-dpq+ielV9/bqgXRUbNH//KsY6WEw9DrGPmipkpmgC1Y46cwuBTNx7PXFWTjc3MQ+urcc0QxoVHcMI0FW4Ok0hg== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/core@10.1.13":
    resolution:
      { integrity: sha512-1viSxebkYN2nJULlzCxES6G9/stgHSepZ9LqqfdIGPHj5OHhiBUXVS0a6R0bEC2A+VL4D9w6QB66ebCr6HGllA== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/editor@4.2.13":
    resolution:
      { integrity: sha512-WbicD9SUQt/K8O5Vyk9iC2ojq5RHoCLK6itpp2fHsWe44VxxcA9z3GTWlvjSTGmMQpZr+lbVmrxdHcumJoLbMA== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/expand@4.0.15":
    resolution:
      { integrity: sha512-4Y+pbr/U9Qcvf+N/goHzPEXiHH8680lM3Dr3Y9h9FFw4gHS+zVpbj8LfbKWIb/jayIB4aSO4pWiBTrBYWkvi5A== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/figures@1.0.12":
    resolution:
      { integrity: sha512-MJttijd8rMFcKJC8NYmprWr6hD3r9Gd9qUC0XwPNwoEPWSMVJwA2MlXxF+nhZZNMY+HXsWa+o7KY2emWYIn0jQ== }
    engines: { node: ">=18" }

  "@inquirer/input@4.1.12":
    resolution:
      { integrity: sha512-xJ6PFZpDjC+tC1P8ImGprgcsrzQRsUh9aH3IZixm1lAZFK49UGHxM3ltFfuInN2kPYNfyoPRh+tU4ftsjPLKqQ== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/number@3.0.15":
    resolution:
      { integrity: sha512-xWg+iYfqdhRiM55MvqiTCleHzszpoigUpN5+t1OMcRkJrUrw7va3AzXaxvS+Ak7Gny0j2mFSTv2JJj8sMtbV2g== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/password@4.0.15":
    resolution:
      { integrity: sha512-75CT2p43DGEnfGTaqFpbDC2p2EEMrq0S+IRrf9iJvYreMy5mAWj087+mdKyLHapUEPLjN10mNvABpGbk8Wdraw== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/prompts@7.5.3":
    resolution:
      { integrity: sha512-8YL0WiV7J86hVAxrh3fE5mDCzcTDe1670unmJRz6ArDgN+DBK1a0+rbnNWp4DUB5rPMwqD5ZP6YHl9KK1mbZRg== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/rawlist@4.1.3":
    resolution:
      { integrity: sha512-7XrV//6kwYumNDSsvJIPeAqa8+p7GJh7H5kRuxirct2cgOcSWwwNGoXDRgpNFbY/MG2vQ4ccIWCi8+IXXyFMZA== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/search@3.0.15":
    resolution:
      { integrity: sha512-YBMwPxYBrADqyvP4nNItpwkBnGGglAvCLVW8u4pRmmvOsHUtCAUIMbUrLX5B3tFL1/WsLGdQ2HNzkqswMs5Uaw== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/select@4.2.3":
    resolution:
      { integrity: sha512-OAGhXU0Cvh0PhLz9xTF/kx6g6x+sP+PcyTiLvCrewI99P3BBeexD+VbuwkNDvqGkk3y2h5ZiWLeRP7BFlhkUDg== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@inquirer/type@3.0.7":
    resolution:
      { integrity: sha512-PfunHQcjwnju84L+ycmcMKB/pTPIngjUJvfnRhKY6FKPuYXlM4aQCb/nIdTFR6BEhMjFvngzvng/vBAJMZpLSA== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  "@isaacs/fs-minipass@4.0.1":
    resolution:
      { integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w== }
    engines: { node: ">=18.0.0" }

  "@jridgewell/gen-mapping@0.3.8":
    resolution:
      { integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA== }
    engines: { node: ">=6.0.0" }

  "@jridgewell/resolve-uri@3.1.2":
    resolution:
      { integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw== }
    engines: { node: ">=6.0.0" }

  "@jridgewell/set-array@1.2.1":
    resolution:
      { integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A== }
    engines: { node: ">=6.0.0" }

  "@jridgewell/source-map@0.3.6":
    resolution:
      { integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ== }

  "@jridgewell/sourcemap-codec@1.5.0":
    resolution:
      { integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ== }

  "@jridgewell/trace-mapping@0.3.25":
    resolution:
      { integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ== }

  "@nodelib/fs.scandir@2.1.5":
    resolution:
      { integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g== }
    engines: { node: ">= 8" }

  "@nodelib/fs.stat@2.0.5":
    resolution:
      { integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A== }
    engines: { node: ">= 8" }

  "@nodelib/fs.walk@1.2.8":
    resolution:
      { integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg== }
    engines: { node: ">= 8" }

  "@rollup/plugin-commonjs@28.0.6":
    resolution:
      { integrity: sha512-XSQB1K7FUU5QP+3lOQmVCE3I0FcbbNvmNT4VJSj93iUjayaARrTQeoRdiYQoftAJBLrR9t2agwAd3ekaTgHNlw== }
    engines: { node: ">=16.0.0 || 14 >= 14.17" }
    peerDependencies:
      rollup: ^2.68.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@rollup/plugin-node-resolve@15.3.1":
    resolution:
      { integrity: sha512-tgg6b91pAybXHJQMAAwW9VuWBO6Thi+q7BCNARLwSqlmsHz0XYURtGvh/AuwSADXSI4h/2uHbs7s4FzlZDGSGA== }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^2.78.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@rollup/plugin-terser@0.4.4":
    resolution:
      { integrity: sha512-XHeJC5Bgvs8LfukDwWZp7yeqin6ns8RTl2B9avbejt6tZqsqvVoWI7ZTQrcNsfKEDWBTnTxM8nMDkO2IFFbd0A== }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@rollup/plugin-typescript@12.1.4":
    resolution:
      { integrity: sha512-s5Hx+EtN60LMlDBvl5f04bEiFZmAepk27Q+mr85L/00zPDn1jtzlTV6FWn81MaIwqfWzKxmOJrBWHU6vtQyedQ== }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^2.14.0||^3.0.0||^4.0.0
      tslib: "*"
      typescript: ">=3.7.0"
    peerDependenciesMeta:
      rollup:
        optional: true
      tslib:
        optional: true

  "@rollup/pluginutils@5.2.0":
    resolution:
      { integrity: sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw== }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@rollup/rollup-android-arm-eabi@4.44.1":
    resolution:
      { integrity: sha512-JAcBr1+fgqx20m7Fwe1DxPUl/hPkee6jA6Pl7n1v2EFiktAHenTaXl5aIFjUIEsfn9w3HE4gK1lEgNGMzBDs1w== }
    cpu: [arm]
    os: [android]

  "@rollup/rollup-android-arm64@4.44.1":
    resolution:
      { integrity: sha512-RurZetXqTu4p+G0ChbnkwBuAtwAbIwJkycw1n6GvlGlBuS4u5qlr5opix8cBAYFJgaY05TWtM+LaoFggUmbZEQ== }
    cpu: [arm64]
    os: [android]

  "@rollup/rollup-darwin-arm64@4.44.1":
    resolution:
      { integrity: sha512-fM/xPesi7g2M7chk37LOnmnSTHLG/v2ggWqKj3CCA1rMA4mm5KVBT1fNoswbo1JhPuNNZrVwpTvlCVggv8A2zg== }
    cpu: [arm64]
    os: [darwin]

  "@rollup/rollup-darwin-x64@4.44.1":
    resolution:
      { integrity: sha512-gDnWk57urJrkrHQ2WVx9TSVTH7lSlU7E3AFqiko+bgjlh78aJ88/3nycMax52VIVjIm3ObXnDL2H00e/xzoipw== }
    cpu: [x64]
    os: [darwin]

  "@rollup/rollup-freebsd-arm64@4.44.1":
    resolution:
      { integrity: sha512-wnFQmJ/zPThM5zEGcnDcCJeYJgtSLjh1d//WuHzhf6zT3Md1BvvhJnWoy+HECKu2bMxaIcfWiu3bJgx6z4g2XA== }
    cpu: [arm64]
    os: [freebsd]

  "@rollup/rollup-freebsd-x64@4.44.1":
    resolution:
      { integrity: sha512-uBmIxoJ4493YATvU2c0upGz87f99e3wop7TJgOA/bXMFd2SvKCI7xkxY/5k50bv7J6dw1SXT4MQBQSLn8Bb/Uw== }
    cpu: [x64]
    os: [freebsd]

  "@rollup/rollup-linux-arm-gnueabihf@4.44.1":
    resolution:
      { integrity: sha512-n0edDmSHlXFhrlmTK7XBuwKlG5MbS7yleS1cQ9nn4kIeW+dJH+ExqNgQ0RrFRew8Y+0V/x6C5IjsHrJmiHtkxQ== }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm-musleabihf@4.44.1":
    resolution:
      { integrity: sha512-8WVUPy3FtAsKSpyk21kV52HCxB+me6YkbkFHATzC2Yd3yuqHwy2lbFL4alJOLXKljoRw08Zk8/xEj89cLQ/4Nw== }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm64-gnu@4.44.1":
    resolution:
      { integrity: sha512-yuktAOaeOgorWDeFJggjuCkMGeITfqvPgkIXhDqsfKX8J3jGyxdDZgBV/2kj/2DyPaLiX6bPdjJDTu9RB8lUPQ== }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-arm64-musl@4.44.1":
    resolution:
      { integrity: sha512-W+GBM4ifET1Plw8pdVaecwUgxmiH23CfAUj32u8knq0JPFyK4weRy6H7ooxYFD19YxBulL0Ktsflg5XS7+7u9g== }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-loongarch64-gnu@4.44.1":
    resolution:
      { integrity: sha512-1zqnUEMWp9WrGVuVak6jWTl4fEtrVKfZY7CvcBmUUpxAJ7WcSowPSAWIKa/0o5mBL/Ij50SIf9tuirGx63Ovew== }
    cpu: [loong64]
    os: [linux]

  "@rollup/rollup-linux-powerpc64le-gnu@4.44.1":
    resolution:
      { integrity: sha512-Rl3JKaRu0LHIx7ExBAAnf0JcOQetQffaw34T8vLlg9b1IhzcBgaIdnvEbbsZq9uZp3uAH+JkHd20Nwn0h9zPjA== }
    cpu: [ppc64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-gnu@4.44.1":
    resolution:
      { integrity: sha512-j5akelU3snyL6K3N/iX7otLBIl347fGwmd95U5gS/7z6T4ftK288jKq3A5lcFKcx7wwzb5rgNvAg3ZbV4BqUSw== }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-musl@4.44.1":
    resolution:
      { integrity: sha512-ppn5llVGgrZw7yxbIm8TTvtj1EoPgYUAbfw0uDjIOzzoqlZlZrLJ/KuiE7uf5EpTpCTrNt1EdtzF0naMm0wGYg== }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-s390x-gnu@4.44.1":
    resolution:
      { integrity: sha512-Hu6hEdix0oxtUma99jSP7xbvjkUM/ycke/AQQ4EC5g7jNRLLIwjcNwaUy95ZKBJJwg1ZowsclNnjYqzN4zwkAw== }
    cpu: [s390x]
    os: [linux]

  "@rollup/rollup-linux-x64-gnu@4.44.1":
    resolution:
      { integrity: sha512-EtnsrmZGomz9WxK1bR5079zee3+7a+AdFlghyd6VbAjgRJDbTANJ9dcPIPAi76uG05micpEL+gPGmAKYTschQw== }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-linux-x64-musl@4.44.1":
    resolution:
      { integrity: sha512-iAS4p+J1az6Usn0f8xhgL4PaU878KEtutP4hqw52I4IO6AGoyOkHCxcc4bqufv1tQLdDWFx8lR9YlwxKuv3/3g== }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-win32-arm64-msvc@4.44.1":
    resolution:
      { integrity: sha512-NtSJVKcXwcqozOl+FwI41OH3OApDyLk3kqTJgx8+gp6On9ZEt5mYhIsKNPGuaZr3p9T6NWPKGU/03Vw4CNU9qg== }
    cpu: [arm64]
    os: [win32]

  "@rollup/rollup-win32-ia32-msvc@4.44.1":
    resolution:
      { integrity: sha512-JYA3qvCOLXSsnTR3oiyGws1Dm0YTuxAAeaYGVlGpUsHqloPcFjPg+X0Fj2qODGLNwQOAcCiQmHub/V007kiH5A== }
    cpu: [ia32]
    os: [win32]

  "@rollup/rollup-win32-x64-msvc@4.44.1":
    resolution:
      { integrity: sha512-J8o22LuF0kTe7m+8PvW9wk3/bRq5+mRo5Dqo6+vXb7otCm3TPhYOJqOaQtGU9YMWQSL3krMnoOxMr0+9E6F3Ug== }
    cpu: [x64]
    os: [win32]

  "@trivago/prettier-plugin-sort-imports@5.2.2":
    resolution:
      { integrity: sha512-fYDQA9e6yTNmA13TLVSA+WMQRc5Bn/c0EUBditUHNfMMxN7M82c38b1kEggVE3pLpZ0FwkwJkUEKMiOi52JXFA== }
    engines: { node: ">18.12" }
    peerDependencies:
      "@vue/compiler-sfc": 3.x
      prettier: 2.x - 3.x
      prettier-plugin-svelte: 3.x
      svelte: 4.x || 5.x
    peerDependenciesMeta:
      "@vue/compiler-sfc":
        optional: true
      prettier-plugin-svelte:
        optional: true
      svelte:
        optional: true

  "@tsconfig/node20@20.1.6":
    resolution:
      { integrity: sha512-sz+Hqx9zwZDpZIV871WSbUzSqNIsXzghZydypnfgzPKLltVJfkINfUeTct31n/tTSa9ZE1ZOfKdRre1uHHquYQ== }

  "@types/estree@1.0.8":
    resolution:
      { integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w== }

  "@types/json-schema@7.0.15":
    resolution:
      { integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA== }

  "@types/luxon@3.6.2":
    resolution:
      { integrity: sha512-R/BdP7OxEMc44l2Ex5lSXHoIXTB2JLNa3y2QISIbr58U/YcsffyQrYW//hZSdrfxrjRZj3GcUoxMPGdO8gSYuw== }

  "@types/node@20.15.0":
    resolution:
      { integrity: sha512-eQf4OkH6gA9v1W0iEpht/neozCsZKMTK+C4cU6/fv7wtJCCL8LEQ4hie2Ln8ZP/0YYM2xGj7//f8xyqItkJ6QA== }

  "@types/resolve@1.20.2":
    resolution:
      { integrity: sha512-60BCwRFOZCQhDncwQdxxeOEEkbc5dIMccYLwbxsS4TUNeVECQ/pBJ0j09mrHOl/JJvpRPGwO9SvE4nR2Nb/a4Q== }

  "@typescript-eslint/eslint-plugin@8.35.0":
    resolution:
      { integrity: sha512-ijItUYaiWuce0N1SoSMrEd0b6b6lYkYt99pqCPfybd+HKVXtEvYhICfLdwp42MhiI5mp0oq7PKEL+g1cNiz/Eg== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      "@typescript-eslint/parser": ^8.35.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/parser@8.35.0":
    resolution:
      { integrity: sha512-6sMvZePQrnZH2/cJkwRpkT7DxoAWh+g6+GFRK6bV3YQo7ogi3SX5rgF6099r5Q53Ma5qeT7LGmOmuIutF4t3lA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/project-service@8.35.0":
    resolution:
      { integrity: sha512-41xatqRwWZuhUMF/aZm2fcUsOFKNcG28xqRSS6ZVr9BVJtGExosLAm5A1OxTjRMagx8nJqva+P5zNIGt8RIgbQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/scope-manager@8.35.0":
    resolution:
      { integrity: sha512-+AgL5+mcoLxl1vGjwNfiWq5fLDZM1TmTPYs2UkyHfFhgERxBbqHlNjRzhThJqz+ktBqTChRYY6zwbMwy0591AA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@typescript-eslint/tsconfig-utils@8.35.0":
    resolution:
      { integrity: sha512-04k/7247kZzFraweuEirmvUj+W3bJLI9fX6fbo1Qm2YykuBvEhRTPl8tcxlYO8kZZW+HIXfkZNoasVb8EV4jpA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/type-utils@8.35.0":
    resolution:
      { integrity: sha512-ceNNttjfmSEoM9PW87bWLDEIaLAyR+E6BoYJQ5PfaDau37UGca9Nyq3lBk8Bw2ad0AKvYabz6wxc7DMTO2jnNA== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/types@8.35.0":
    resolution:
      { integrity: sha512-0mYH3emanku0vHw2aRLNGqe7EXh9WHEhi7kZzscrMDf6IIRUQ5Jk4wp1QrledE/36KtdZrVfKnE32eZCf/vaVQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@typescript-eslint/typescript-estree@8.35.0":
    resolution:
      { integrity: sha512-F+BhnaBemgu1Qf8oHrxyw14wq6vbL8xwWKKMwTMwYIRmFFY/1n/9T/jpbobZL8vp7QyEUcC6xGrnAO4ua8Kp7w== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/utils@8.35.0":
    resolution:
      { integrity: sha512-nqoMu7WWM7ki5tPgLVsmPM8CkqtoPUG6xXGeefM5t4x3XumOEKMoUZPdi+7F+/EotukN4R9OWdmDxN80fqoZeg== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ">=4.8.4 <5.9.0"

  "@typescript-eslint/visitor-keys@8.35.0":
    resolution:
      { integrity: sha512-zTh2+1Y8ZpmeQaQVIc/ZZxsx8UzgKJyNg1PTvjzC7WMhPSVS8bfDX34k1SrwOf016qd5RU3az2UxUNue3IfQ5g== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  "@zip.js/zip.js@2.7.62":
    resolution:
      { integrity: sha512-OaLvZ8j4gCkLn048ypkZu29KX30r8/OfFF2w4Jo5WXFr+J04J+lzJ5TKZBVgFXhlvSkqNFQdfnY1Q8TMTCyBVA== }
    engines: { bun: ">=0.7.0", deno: ">=1.0.0", node: ">=16.5.0" }

  acorn-jsx@5.3.2:
    resolution:
      { integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ== }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution:
      { integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg== }
    engines: { node: ">=0.4.0" }
    hasBin: true

  ajv@6.12.6:
    resolution:
      { integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g== }

  ajv@8.17.1:
    resolution:
      { integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g== }

  ansi-escapes@4.3.2:
    resolution:
      { integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ== }
    engines: { node: ">=8" }

  ansi-regex@5.0.1:
    resolution:
      { integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ== }
    engines: { node: ">=8" }

  ansi-styles@4.3.0:
    resolution:
      { integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg== }
    engines: { node: ">=8" }

  ansi-styles@6.2.1:
    resolution:
      { integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug== }
    engines: { node: ">=12" }

  are-docs-informative@0.0.2:
    resolution:
      { integrity: sha512-ixiS0nLNNG5jNQzgZJNoUpBKdo9yTYZMGJ+QgT2jmjR7G7+QHRCc4v6LQ3NgE7EBJq+o0ams3waJwkrlBom8Ig== }
    engines: { node: ">=14" }

  argparse@2.0.1:
    resolution:
      { integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q== }

  async@3.2.6:
    resolution:
      { integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA== }

  balanced-match@1.0.2:
    resolution:
      { integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw== }

  brace-expansion@1.1.12:
    resolution:
      { integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg== }

  brace-expansion@2.0.2:
    resolution:
      { integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ== }

  braces@3.0.3:
    resolution:
      { integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA== }
    engines: { node: ">=8" }

  browser-or-node@3.0.0:
    resolution:
      { integrity: sha512-iczIdVJzGEYhP5DqQxYM9Hh7Ztpqqi+CXZpSmX8ALFs9ecXkQIeqRyM6TfxEfMVpwhl3dSuDvxdzzo9sUOIVBQ== }

  buffer-from@1.1.2:
    resolution:
      { integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ== }

  callsites@3.1.0:
    resolution:
      { integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ== }
    engines: { node: ">=6" }

  chalk@4.1.2:
    resolution:
      { integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA== }
    engines: { node: ">=10" }

  chalk@5.4.1:
    resolution:
      { integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w== }
    engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

  chardet@0.7.0:
    resolution:
      { integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA== }

  chownr@3.0.0:
    resolution:
      { integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g== }
    engines: { node: ">=18" }

  cli-width@4.1.0:
    resolution:
      { integrity: sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ== }
    engines: { node: ">= 12" }

  color-convert@2.0.1:
    resolution:
      { integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ== }
    engines: { node: ">=7.0.0" }

  color-name@1.1.4:
    resolution:
      { integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA== }

  commander@12.1.0:
    resolution:
      { integrity: sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA== }
    engines: { node: ">=18" }

  commander@13.1.0:
    resolution:
      { integrity: sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw== }
    engines: { node: ">=18" }

  commander@2.20.3:
    resolution:
      { integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ== }

  comment-parser@1.4.1:
    resolution:
      { integrity: sha512-buhp5kePrmda3vhc5B9t7pUQXAb2Tnd0qgpkIhPhkHXxJpiPJ11H0ZEU0oBpJ2QztSbzG/ZxMj/CHsYJqRHmyg== }
    engines: { node: ">= 12.0.0" }

  commondir@1.0.1:
    resolution:
      { integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg== }

  concat-map@0.0.1:
    resolution:
      { integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg== }

  cross-spawn@7.0.6:
    resolution:
      { integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA== }
    engines: { node: ">= 8" }

  debug@4.4.1:
    resolution:
      { integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ== }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-eql@5.0.2:
    resolution:
      { integrity: sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q== }
    engines: { node: ">=6" }

  deep-is@0.1.4:
    resolution:
      { integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ== }

  deepcopy-esm@2.1.1:
    resolution:
      { integrity: sha512-0lopQd/gi3excE3sgBrjuR3gJv6ZElk027i30pUgdjtvSJl/OoZ8B6L42GUBm6C3G8hD1EB5ir2gTYnINzWx4g== }
    engines: { node: ">=22" }

  deepmerge@4.3.1:
    resolution:
      { integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A== }
    engines: { node: ">=0.10.0" }

  ejs@3.1.10:
    resolution:
      { integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA== }
    engines: { node: ">=0.10.0" }
    hasBin: true

  emoji-regex@8.0.0:
    resolution:
      { integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A== }

  escape-string-regexp@4.0.0:
    resolution:
      { integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA== }
    engines: { node: ">=10" }

  eslint-plugin-jsdoc@50.8.0:
    resolution:
      { integrity: sha512-UyGb5755LMFWPrZTEqqvTJ3urLz1iqj+bYOHFNag+sw3NvaMWP9K2z+uIn37XfNALmQLQyrBlJ5mkiVPL7ADEg== }
    engines: { node: ">=18" }
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0 || ^9.0.0

  eslint-scope@8.4.0:
    resolution:
      { integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  eslint-visitor-keys@3.4.3:
    resolution:
      { integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag== }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint-visitor-keys@4.2.1:
    resolution:
      { integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  eslint@9.30.0:
    resolution:
      { integrity: sha512-iN/SiPxmQu6EVkf+m1qpBxzUhE12YqFLOSySuOyVLJLEF9nzTf+h/1AJYc1JWzCnktggeNrjvQGLngDzXirU6g== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    hasBin: true
    peerDependencies:
      jiti: "*"
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution:
      { integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  esquery@1.6.0:
    resolution:
      { integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg== }
    engines: { node: ">=0.10" }

  esrecurse@4.3.0:
    resolution:
      { integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag== }
    engines: { node: ">=4.0" }

  estraverse@5.3.0:
    resolution:
      { integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA== }
    engines: { node: ">=4.0" }

  estree-walker@2.0.2:
    resolution:
      { integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w== }

  esutils@2.0.3:
    resolution:
      { integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g== }
    engines: { node: ">=0.10.0" }

  expect-type@1.2.1:
    resolution:
      { integrity: sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw== }
    engines: { node: ">=12.0.0" }

  external-editor@3.1.0:
    resolution:
      { integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew== }
    engines: { node: ">=4" }

  fast-deep-equal@3.1.3:
    resolution:
      { integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q== }

  fast-glob@3.3.3:
    resolution:
      { integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg== }
    engines: { node: ">=8.6.0" }

  fast-json-stable-stringify@2.1.0:
    resolution:
      { integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw== }

  fast-levenshtein@2.0.6:
    resolution:
      { integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw== }

  fast-uri@3.0.6:
    resolution:
      { integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw== }

  fastq@1.19.1:
    resolution:
      { integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ== }

  fdir@6.4.6:
    resolution:
      { integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w== }
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-entry-cache@8.0.0:
    resolution:
      { integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ== }
    engines: { node: ">=16.0.0" }

  filelist@1.0.4:
    resolution:
      { integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q== }

  fill-range@7.1.1:
    resolution:
      { integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg== }
    engines: { node: ">=8" }

  find-process@1.4.10:
    resolution:
      { integrity: sha512-ncYFnWEIwL7PzmrK1yZtaccN8GhethD37RzBHG6iOZoFYB4vSmLLXfeWJjeN5nMvCJMjOtBvBBF8OgxEcikiZg== }
    hasBin: true

  find-up@5.0.0:
    resolution:
      { integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng== }
    engines: { node: ">=10" }

  flat-cache@4.0.1:
    resolution:
      { integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw== }
    engines: { node: ">=16" }

  flatted@3.3.3:
    resolution:
      { integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg== }

  fsevents@2.3.3:
    resolution:
      { integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw== }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      { integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA== }

  glob-parent@5.1.2:
    resolution:
      { integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow== }
    engines: { node: ">= 6" }

  glob-parent@6.0.2:
    resolution:
      { integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A== }
    engines: { node: ">=10.13.0" }

  globals@11.12.0:
    resolution:
      { integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA== }
    engines: { node: ">=4" }

  globals@14.0.0:
    resolution:
      { integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ== }
    engines: { node: ">=18" }

  graphemer@1.4.0:
    resolution:
      { integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag== }

  has-flag@4.0.0:
    resolution:
      { integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ== }
    engines: { node: ">=8" }

  hasown@2.0.2:
    resolution:
      { integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ== }
    engines: { node: ">= 0.4" }

  iconv-lite@0.4.24:
    resolution:
      { integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA== }
    engines: { node: ">=0.10.0" }

  ignore@5.3.2:
    resolution:
      { integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g== }
    engines: { node: ">= 4" }

  ignore@7.0.5:
    resolution:
      { integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg== }
    engines: { node: ">= 4" }

  import-fresh@3.3.1:
    resolution:
      { integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ== }
    engines: { node: ">=6" }

  imurmurhash@0.1.4:
    resolution:
      { integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA== }
    engines: { node: ">=0.8.19" }

  inquirer@12.6.3:
    resolution:
      { integrity: sha512-eX9beYAjr1MqYsIjx1vAheXsRk1jbZRvHLcBu5nA9wX0rXR1IfCZLnVLp4Ym4mrhqmh7AuANwcdtgQ291fZDfQ== }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true

  is-core-module@2.16.1:
    resolution:
      { integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w== }
    engines: { node: ">= 0.4" }

  is-extglob@2.1.1:
    resolution:
      { integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ== }
    engines: { node: ">=0.10.0" }

  is-fullwidth-code-point@3.0.0:
    resolution:
      { integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg== }
    engines: { node: ">=8" }

  is-glob@4.0.3:
    resolution:
      { integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg== }
    engines: { node: ">=0.10.0" }

  is-interactive@2.0.0:
    resolution:
      { integrity: sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ== }
    engines: { node: ">=12" }

  is-module@1.0.0:
    resolution:
      { integrity: sha512-51ypPSPCoTEIN9dy5Oy+h4pShgJmPCygKfyRCISBI+JoWT/2oJvK8QPxmwv7b/p239jXrm9M1mlQbyKJ5A152g== }

  is-number@7.0.0:
    resolution:
      { integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng== }
    engines: { node: ">=0.12.0" }

  is-reference@1.2.1:
    resolution:
      { integrity: sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ== }

  is-unicode-supported@2.1.0:
    resolution:
      { integrity: sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ== }
    engines: { node: ">=18" }

  isexe@2.0.0:
    resolution:
      { integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw== }

  jake@10.9.2:
    resolution:
      { integrity: sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA== }
    engines: { node: ">=10" }
    hasBin: true

  javascript-natural-sort@0.7.1:
    resolution:
      { integrity: sha512-nO6jcEfZWQXDhOiBtG2KvKyEptz7RVbpGP4vTD2hLBdmNQSsCiicO2Ioinv6UI4y9ukqnBpy+XZ9H6uLNgJTlw== }

  js-tokens@4.0.0:
    resolution:
      { integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ== }

  js-yaml@4.1.0:
    resolution:
      { integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA== }
    hasBin: true

  jsdoc-type-pratt-parser@4.1.0:
    resolution:
      { integrity: sha512-Hicd6JK5Njt2QB6XYFS7ok9e37O8AYk3jTcppG4YVQnYjOemymvTcmc7OWsmq/Qqj5TdRFO5/x/tIPmBeRtGHg== }
    engines: { node: ">=12.0.0" }

  jsesc@3.1.0:
    resolution:
      { integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA== }
    engines: { node: ">=6" }
    hasBin: true

  json-buffer@3.0.1:
    resolution:
      { integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ== }

  json-schema-traverse@0.4.1:
    resolution:
      { integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg== }

  json-schema-traverse@1.0.0:
    resolution:
      { integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug== }

  json-stable-stringify-without-jsonify@1.0.1:
    resolution:
      { integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw== }

  json5@2.2.3:
    resolution:
      { integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg== }
    engines: { node: ">=6" }
    hasBin: true

  keyv@4.5.4:
    resolution:
      { integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw== }

  levn@0.4.1:
    resolution:
      { integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ== }
    engines: { node: ">= 0.8.0" }

  locate-path@6.0.0:
    resolution:
      { integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw== }
    engines: { node: ">=10" }

  lodash.merge@4.6.2:
    resolution:
      { integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ== }

  lodash@4.17.21:
    resolution:
      { integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg== }

  log-symbols@7.0.1:
    resolution:
      { integrity: sha512-ja1E3yCr9i/0hmBVaM0bfwDjnGy8I/s6PP4DFp+yP+a+mrHO4Rm7DtmnqROTUkHIkqffC84YY7AeqX6oFk0WFg== }
    engines: { node: ">=18" }

  loglevel@1.9.2:
    resolution:
      { integrity: sha512-HgMmCqIJSAKqo68l0rS2AanEWfkxaZ5wNiEFb5ggm08lDs9Xl2KxBlX3PTcaD2chBM1gXAYf491/M2Rv8Jwayg== }
    engines: { node: ">= 0.6.0" }

  luxon@3.6.1:
    resolution:
      { integrity: sha512-tJLxrKJhO2ukZ5z0gyjY1zPh3Rh88Ej9P7jNrZiHMUXHae1yvI2imgOZtL1TO8TW6biMMKfTtAOoEJANgtWBMQ== }
    engines: { node: ">=12" }

  magic-string@0.30.17:
    resolution:
      { integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA== }

  merge2@1.4.1:
    resolution:
      { integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg== }
    engines: { node: ">= 8" }

  micromatch@4.0.8:
    resolution:
      { integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA== }
    engines: { node: ">=8.6" }

  minimatch@3.1.2:
    resolution:
      { integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw== }

  minimatch@5.1.6:
    resolution:
      { integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g== }
    engines: { node: ">=10" }

  minimatch@9.0.5:
    resolution:
      { integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow== }
    engines: { node: ">=16 || 14 >=14.17" }

  minipass@7.1.2:
    resolution:
      { integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw== }
    engines: { node: ">=16 || 14 >=14.17" }

  minizlib@3.0.2:
    resolution:
      { integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA== }
    engines: { node: ">= 18" }

  mkdirp@3.0.1:
    resolution:
      { integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg== }
    engines: { node: ">=10" }
    hasBin: true

  ms@2.1.3:
    resolution:
      { integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA== }

  mute-stream@2.0.0:
    resolution:
      { integrity: sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA== }
    engines: { node: ^18.17.0 || >=20.5.0 }

  natural-compare@1.4.0:
    resolution:
      { integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw== }

  optionator@0.9.4:
    resolution:
      { integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g== }
    engines: { node: ">= 0.8.0" }

  os-tmpdir@1.0.2:
    resolution:
      { integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g== }
    engines: { node: ">=0.10.0" }

  p-limit@3.1.0:
    resolution:
      { integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ== }
    engines: { node: ">=10" }

  p-locate@5.0.0:
    resolution:
      { integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw== }
    engines: { node: ">=10" }

  parent-module@1.0.1:
    resolution:
      { integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g== }
    engines: { node: ">=6" }

  parse-imports-exports@0.2.4:
    resolution:
      { integrity: sha512-4s6vd6dx1AotCx/RCI2m7t7GCh5bDRUtGNvRfHSP2wbBQdMi67pPe7mtzmgwcaQ8VKK/6IB7Glfyu3qdZJPybQ== }

  parse-statements@1.0.11:
    resolution:
      { integrity: sha512-HlsyYdMBnbPQ9Jr/VgJ1YF4scnldvJpJxCVx6KgqPL4dxppsWrJHCIIxQXMJrqGnsRkNPATbeMJ8Yxu7JMsYcA== }

  path-exists@4.0.0:
    resolution:
      { integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w== }
    engines: { node: ">=8" }

  path-key@3.1.1:
    resolution:
      { integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q== }
    engines: { node: ">=8" }

  path-parse@1.0.7:
    resolution:
      { integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw== }

  picocolors@1.1.1:
    resolution:
      { integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA== }

  picomatch@2.3.1:
    resolution:
      { integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA== }
    engines: { node: ">=8.6" }

  picomatch@4.0.2:
    resolution:
      { integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg== }
    engines: { node: ">=12" }

  prelude-ls@1.2.1:
    resolution:
      { integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g== }
    engines: { node: ">= 0.8.0" }

  prettier-plugin-multiline-arrays@4.0.3:
    resolution:
      { integrity: sha512-H1f/0zbvlO/FR0Fmyl31sSBodsIZkuQF0Omi9BrptLU31rZ+Almt9BbrE8IS3BFT/DGKePKb55XqN660LTnmsQ== }
    engines: { node: ">=20" }
    peerDependencies:
      prettier: ">=3.0.0"

  prettier@3.6.2:
    resolution:
      { integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ== }
    engines: { node: ">=14" }
    hasBin: true

  proxy-vir@2.0.1:
    resolution:
      { integrity: sha512-hjy5mWzHZhgRGh0f90f0Bz3VrGUe0T+AlhwnETakzRdvaN9RtPYLQG1+ZuEzSDK95FAhPYd26nEi1xVrXqvBwg== }
    engines: { node: ">=22" }

  punycode@2.3.1:
    resolution:
      { integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg== }
    engines: { node: ">=6" }

  queue-microtask@1.2.3:
    resolution:
      { integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A== }

  rage-edit@1.2.0:
    resolution:
      { integrity: sha512-0RspBRc2s6We4g7hRCvT5mu7YPEnfjvQK8Tt354a2uUNJCMC7MKLvo/1mLvHUCQ/zbP6siQyp5VRZN7UCpMFZg== }

  randombytes@2.1.0:
    resolution:
      { integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ== }

  require-from-string@2.0.2:
    resolution:
      { integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw== }
    engines: { node: ">=0.10.0" }

  resolve-from@4.0.0:
    resolution:
      { integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g== }
    engines: { node: ">=4" }

  resolve@1.22.10:
    resolution:
      { integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w== }
    engines: { node: ">= 0.4" }
    hasBin: true

  reusify@1.1.0:
    resolution:
      { integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw== }
    engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

  rollup@4.44.1:
    resolution:
      { integrity: sha512-x8H8aPvD+xbl0Do8oez5f5o8eMS3trfCghc4HhLAnCkj7Vl0d1JWGs0UF/D886zLW2rOj2QymV/JcSSsw+XDNg== }
    engines: { node: ">=18.0.0", npm: ">=8.0.0" }
    hasBin: true

  run-async@3.0.0:
    resolution:
      { integrity: sha512-540WwVDOMxA6dN6We19EcT9sc3hkXPw5mzRNGM3FkdN/vtE9NFvj5lFAPNwUDmJjXidm3v7TC1cTE7t17Ulm1Q== }
    engines: { node: ">=0.12.0" }

  run-parallel@1.2.0:
    resolution:
      { integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA== }

  rxjs@7.8.2:
    resolution:
      { integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA== }

  safe-buffer@5.2.1:
    resolution:
      { integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ== }

  safer-buffer@2.1.2:
    resolution:
      { integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg== }

  semver@7.7.2:
    resolution:
      { integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA== }
    engines: { node: ">=10" }
    hasBin: true

  serialize-javascript@6.0.2:
    resolution:
      { integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g== }

  shebang-command@2.0.0:
    resolution:
      { integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA== }
    engines: { node: ">=8" }

  shebang-regex@3.0.0:
    resolution:
      { integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A== }
    engines: { node: ">=8" }

  signal-exit@4.1.0:
    resolution:
      { integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw== }
    engines: { node: ">=14" }

  smob@1.5.0:
    resolution:
      { integrity: sha512-g6T+p7QO8npa+/hNx9ohv1E5pVCmWrVCUzUXJyLdMmftX6ER0oiWY/w9knEonLpnOp6b6FenKnMfR8gqwWdwig== }

  source-map-support@0.5.21:
    resolution:
      { integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w== }

  source-map@0.6.1:
    resolution:
      { integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g== }
    engines: { node: ">=0.10.0" }

  spdx-exceptions@2.5.0:
    resolution:
      { integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w== }

  spdx-expression-parse@4.0.0:
    resolution:
      { integrity: sha512-Clya5JIij/7C6bRR22+tnGXbc4VKlibKSVj2iHvVeX5iMW7s1SIQlqu699JkODJJIhh/pUu8L0/VLh8xflD+LQ== }

  spdx-license-ids@3.0.21:
    resolution:
      { integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg== }

  string-width@4.2.3:
    resolution:
      { integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g== }
    engines: { node: ">=8" }

  strip-ansi@6.0.1:
    resolution:
      { integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A== }
    engines: { node: ">=8" }

  strip-json-comments@3.1.1:
    resolution:
      { integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig== }
    engines: { node: ">=8" }

  supports-color@7.2.0:
    resolution:
      { integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw== }
    engines: { node: ">=8" }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      { integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w== }
    engines: { node: ">= 0.4" }

  tar@7.4.3:
    resolution:
      { integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw== }
    engines: { node: ">=18" }

  terser@5.43.1:
    resolution:
      { integrity: sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg== }
    engines: { node: ">=10" }
    hasBin: true

  tmp@0.0.33:
    resolution:
      { integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw== }
    engines: { node: ">=0.6.0" }

  to-regex-range@5.0.1:
    resolution:
      { integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ== }
    engines: { node: ">=8.0" }

  ts-api-utils@2.1.0:
    resolution:
      { integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ== }
    engines: { node: ">=18.12" }
    peerDependencies:
      typescript: ">=4.8.4"

  tslib@2.8.1:
    resolution:
      { integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w== }

  type-check@0.4.0:
    resolution:
      { integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew== }
    engines: { node: ">= 0.8.0" }

  type-fest@0.21.3:
    resolution:
      { integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w== }
    engines: { node: ">=10" }

  type-fest@4.41.0:
    resolution:
      { integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA== }
    engines: { node: ">=16" }

  typed-event-target@4.1.0:
    resolution:
      { integrity: sha512-fDFhZb7ofywLsVv8mYePD6ONfCpVHyM1t2dboEJx/XMsnflljnu3GQ5qH09hS1USuypGMR7wRbdWQPydgJ8nGQ== }
    engines: { node: ">=22" }

  typescript-eslint@8.35.0:
    resolution:
      { integrity: sha512-uEnz70b7kBz6eg/j0Czy6K5NivaYopgxRjsnAJ2Fx5oTLo3wefTHIbL7AkQr1+7tJCRVpTs/wiM8JR/11Loq9A== }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ">=4.8.4 <5.9.0"

  typescript@5.8.3:
    resolution:
      { integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ== }
    engines: { node: ">=14.17" }
    hasBin: true

  undici-types@6.13.0:
    resolution:
      { integrity: sha512-xtFJHudx8S2DSoujjMd1WeWvn7KKWFRESZTMeL1RptAYERu29D6jphMjjY+vn96jvN3kVPDNxU/E13VTaXj6jg== }

  uri-js@4.4.1:
    resolution:
      { integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg== }

  which@2.0.2:
    resolution:
      { integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA== }
    engines: { node: ">= 8" }
    hasBin: true

  word-wrap@1.2.5:
    resolution:
      { integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA== }
    engines: { node: ">=0.10.0" }

  wrap-ansi@6.2.0:
    resolution:
      { integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA== }
    engines: { node: ">=8" }

  ws@8.18.3:
    resolution:
      { integrity: sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg== }
    engines: { node: ">=10.0.0" }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ">=5.0.2"
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  yallist@5.0.0:
    resolution:
      { integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw== }
    engines: { node: ">=18" }

  yocto-queue@0.1.0:
    resolution:
      { integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q== }
    engines: { node: ">=10" }

  yoctocolors-cjs@2.1.2:
    resolution:
      { integrity: sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA== }
    engines: { node: ">=18" }

  yoctocolors@2.1.1:
    resolution:
      { integrity: sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ== }
    engines: { node: ">=18" }

snapshots:
  "@augment-vir/assert@31.26.0":
    dependencies:
      "@augment-vir/core": 31.26.0
      "@date-vir/duration": 7.3.2
      deep-eql: 5.0.2
      expect-type: 1.2.1
      type-fest: 4.41.0

  "@augment-vir/common@31.26.0":
    dependencies:
      "@augment-vir/assert": 31.26.0
      "@augment-vir/core": 31.26.0
      "@date-vir/duration": 7.3.2
      ansi-styles: 6.2.1
      deepcopy-esm: 2.1.1
      json5: 2.2.3
      type-fest: 4.41.0
      typed-event-target: 4.1.0

  "@augment-vir/core@31.26.0":
    dependencies:
      "@date-vir/duration": 7.3.2
      browser-or-node: 3.0.0
      json5: 2.2.3
      type-fest: 4.41.0

  "@babel/code-frame@7.27.1":
    dependencies:
      "@babel/helper-validator-identifier": 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  "@babel/generator@7.27.5":
    dependencies:
      "@babel/parser": 7.27.7
      "@babel/types": 7.27.7
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25
      jsesc: 3.1.0

  "@babel/helper-string-parser@7.27.1": {}

  "@babel/helper-validator-identifier@7.27.1": {}

  "@babel/parser@7.27.7":
    dependencies:
      "@babel/types": 7.27.7

  "@babel/template@7.27.2":
    dependencies:
      "@babel/code-frame": 7.27.1
      "@babel/parser": 7.27.7
      "@babel/types": 7.27.7

  "@babel/traverse@7.27.7":
    dependencies:
      "@babel/code-frame": 7.27.1
      "@babel/generator": 7.27.5
      "@babel/parser": 7.27.7
      "@babel/template": 7.27.2
      "@babel/types": 7.27.7
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  "@babel/types@7.27.7":
    dependencies:
      "@babel/helper-string-parser": 7.27.1
      "@babel/helper-validator-identifier": 7.27.1

  "@date-vir/duration@7.3.2":
    dependencies:
      "@types/luxon": 3.6.2
      luxon: 3.6.1
      type-fest: 4.41.0

  "@elgato/cli@1.4.0(@types/node@20.15.0)":
    dependencies:
      "@elgato/schemas": 0.4.6
      "@humanwhocodes/momoa": 3.3.8
      "@zip.js/zip.js": 2.7.62
      ajv: 8.17.1
      chalk: 5.4.1
      commander: 13.1.0
      ejs: 3.1.10
      find-process: 1.4.10
      ignore: 7.0.5
      inquirer: 12.6.3(@types/node@20.15.0)
      is-interactive: 2.0.0
      lodash: 4.17.21
      log-symbols: 7.0.1
      rage-edit: 1.2.0
      semver: 7.7.2
      tar: 7.4.3
    transitivePeerDependencies:
      - "@types/node"

  "@elgato/eslint-config@0.1.0(@eslint/js@9.30.0)(eslint-plugin-jsdoc@50.8.0(eslint@9.30.0))(eslint@9.30.0)(typescript-eslint@8.35.0(eslint@9.30.0)(typescript@5.8.3))(typescript@5.8.3)":
    dependencies:
      "@eslint/js": 9.30.0
      eslint: 9.30.0
      eslint-plugin-jsdoc: 50.8.0(eslint@9.30.0)
      typescript: 5.8.3
      typescript-eslint: 8.35.0(eslint@9.30.0)(typescript@5.8.3)

  "@elgato/prettier-config@0.2.6(@trivago/prettier-plugin-sort-imports@5.2.2(prettier@3.6.2))(prettier-plugin-multiline-arrays@4.0.3(prettier@3.6.2))(prettier@3.6.2)":
    dependencies:
      "@trivago/prettier-plugin-sort-imports": 5.2.2(prettier@3.6.2)
      prettier: 3.6.2
      prettier-plugin-multiline-arrays: 4.0.3(prettier@3.6.2)

  "@elgato/schemas@0.4.6": {}

  "@elgato/streamdeck@1.3.1":
    dependencies:
      "@elgato/schemas": 0.4.6
      ws: 8.18.3
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  "@es-joy/jsdoccomment@0.50.2":
    dependencies:
      "@types/estree": 1.0.8
      "@typescript-eslint/types": 8.35.0
      comment-parser: 1.4.1
      esquery: 1.6.0
      jsdoc-type-pratt-parser: 4.1.0

  "@eslint-community/eslint-utils@4.7.0(eslint@9.30.0)":
    dependencies:
      eslint: 9.30.0
      eslint-visitor-keys: 3.4.3

  "@eslint-community/regexpp@4.12.1": {}

  "@eslint/config-array@0.21.0":
    dependencies:
      "@eslint/object-schema": 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  "@eslint/config-helpers@0.3.0": {}

  "@eslint/core@0.14.0":
    dependencies:
      "@types/json-schema": 7.0.15

  "@eslint/core@0.15.1":
    dependencies:
      "@types/json-schema": 7.0.15

  "@eslint/eslintrc@3.3.1":
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  "@eslint/js@9.30.0": {}

  "@eslint/object-schema@2.1.6": {}

  "@eslint/plugin-kit@0.3.3":
    dependencies:
      "@eslint/core": 0.15.1
      levn: 0.4.1

  "@humanfs/core@0.19.1": {}

  "@humanfs/node@0.16.6":
    dependencies:
      "@humanfs/core": 0.19.1
      "@humanwhocodes/retry": 0.3.1

  "@humanwhocodes/module-importer@1.0.1": {}

  "@humanwhocodes/momoa@3.3.8": {}

  "@humanwhocodes/retry@0.3.1": {}

  "@humanwhocodes/retry@0.4.3": {}

  "@inquirer/checkbox@4.1.8(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/figures": 1.0.12
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      ansi-escapes: 4.3.2
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/confirm@5.1.12(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/core@10.1.13(@types/node@20.15.0)":
    dependencies:
      "@inquirer/figures": 1.0.12
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      ansi-escapes: 4.3.2
      cli-width: 4.1.0
      mute-stream: 2.0.0
      signal-exit: 4.1.0
      wrap-ansi: 6.2.0
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/editor@4.2.13(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      external-editor: 3.1.0
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/expand@4.0.15(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/figures@1.0.12": {}

  "@inquirer/input@4.1.12(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/number@3.0.15(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/password@4.0.15(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      ansi-escapes: 4.3.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/prompts@7.5.3(@types/node@20.15.0)":
    dependencies:
      "@inquirer/checkbox": 4.1.8(@types/node@20.15.0)
      "@inquirer/confirm": 5.1.12(@types/node@20.15.0)
      "@inquirer/editor": 4.2.13(@types/node@20.15.0)
      "@inquirer/expand": 4.0.15(@types/node@20.15.0)
      "@inquirer/input": 4.1.12(@types/node@20.15.0)
      "@inquirer/number": 3.0.15(@types/node@20.15.0)
      "@inquirer/password": 4.0.15(@types/node@20.15.0)
      "@inquirer/rawlist": 4.1.3(@types/node@20.15.0)
      "@inquirer/search": 3.0.15(@types/node@20.15.0)
      "@inquirer/select": 4.2.3(@types/node@20.15.0)
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/rawlist@4.1.3(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/search@3.0.15(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/figures": 1.0.12
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/select@4.2.3(@types/node@20.15.0)":
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/figures": 1.0.12
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      ansi-escapes: 4.3.2
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      "@types/node": 20.15.0

  "@inquirer/type@3.0.7(@types/node@20.15.0)":
    optionalDependencies:
      "@types/node": 20.15.0

  "@isaacs/fs-minipass@4.0.1":
    dependencies:
      minipass: 7.1.2

  "@jridgewell/gen-mapping@0.3.8":
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.5.0
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/resolve-uri@3.1.2": {}

  "@jridgewell/set-array@1.2.1": {}

  "@jridgewell/source-map@0.3.6":
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/sourcemap-codec@1.5.0": {}

  "@jridgewell/trace-mapping@0.3.25":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.5.0

  "@nodelib/fs.scandir@2.1.5":
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      run-parallel: 1.2.0

  "@nodelib/fs.stat@2.0.5": {}

  "@nodelib/fs.walk@1.2.8":
    dependencies:
      "@nodelib/fs.scandir": 2.1.5
      fastq: 1.19.1

  "@rollup/plugin-commonjs@28.0.6(rollup@4.44.1)":
    dependencies:
      "@rollup/pluginutils": 5.2.0(rollup@4.44.1)
      commondir: 1.0.1
      estree-walker: 2.0.2
      fdir: 6.4.6(picomatch@4.0.2)
      is-reference: 1.2.1
      magic-string: 0.30.17
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.44.1

  "@rollup/plugin-node-resolve@15.3.1(rollup@4.44.1)":
    dependencies:
      "@rollup/pluginutils": 5.2.0(rollup@4.44.1)
      "@types/resolve": 1.20.2
      deepmerge: 4.3.1
      is-module: 1.0.0
      resolve: 1.22.10
    optionalDependencies:
      rollup: 4.44.1

  "@rollup/plugin-terser@0.4.4(rollup@4.44.1)":
    dependencies:
      serialize-javascript: 6.0.2
      smob: 1.5.0
      terser: 5.43.1
    optionalDependencies:
      rollup: 4.44.1

  "@rollup/plugin-typescript@12.1.4(rollup@4.44.1)(tslib@2.8.1)(typescript@5.8.3)":
    dependencies:
      "@rollup/pluginutils": 5.2.0(rollup@4.44.1)
      resolve: 1.22.10
      typescript: 5.8.3
    optionalDependencies:
      rollup: 4.44.1
      tslib: 2.8.1

  "@rollup/pluginutils@5.2.0(rollup@4.44.1)":
    dependencies:
      "@types/estree": 1.0.8
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.44.1

  "@rollup/rollup-android-arm-eabi@4.44.1":
    optional: true

  "@rollup/rollup-android-arm64@4.44.1":
    optional: true

  "@rollup/rollup-darwin-arm64@4.44.1":
    optional: true

  "@rollup/rollup-darwin-x64@4.44.1":
    optional: true

  "@rollup/rollup-freebsd-arm64@4.44.1":
    optional: true

  "@rollup/rollup-freebsd-x64@4.44.1":
    optional: true

  "@rollup/rollup-linux-arm-gnueabihf@4.44.1":
    optional: true

  "@rollup/rollup-linux-arm-musleabihf@4.44.1":
    optional: true

  "@rollup/rollup-linux-arm64-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-arm64-musl@4.44.1":
    optional: true

  "@rollup/rollup-linux-loongarch64-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-powerpc64le-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-riscv64-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-riscv64-musl@4.44.1":
    optional: true

  "@rollup/rollup-linux-s390x-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-x64-gnu@4.44.1":
    optional: true

  "@rollup/rollup-linux-x64-musl@4.44.1":
    optional: true

  "@rollup/rollup-win32-arm64-msvc@4.44.1":
    optional: true

  "@rollup/rollup-win32-ia32-msvc@4.44.1":
    optional: true

  "@rollup/rollup-win32-x64-msvc@4.44.1":
    optional: true

  "@trivago/prettier-plugin-sort-imports@5.2.2(prettier@3.6.2)":
    dependencies:
      "@babel/generator": 7.27.5
      "@babel/parser": 7.27.7
      "@babel/traverse": 7.27.7
      "@babel/types": 7.27.7
      javascript-natural-sort: 0.7.1
      lodash: 4.17.21
      prettier: 3.6.2
    transitivePeerDependencies:
      - supports-color

  "@tsconfig/node20@20.1.6": {}

  "@types/estree@1.0.8": {}

  "@types/json-schema@7.0.15": {}

  "@types/luxon@3.6.2": {}

  "@types/node@20.15.0":
    dependencies:
      undici-types: 6.13.0

  "@types/resolve@1.20.2": {}

  "@typescript-eslint/eslint-plugin@8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.30.0)(typescript@5.8.3))(eslint@9.30.0)(typescript@5.8.3)":
    dependencies:
      "@eslint-community/regexpp": 4.12.1
      "@typescript-eslint/parser": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      "@typescript-eslint/scope-manager": 8.35.0
      "@typescript-eslint/type-utils": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      "@typescript-eslint/utils": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      "@typescript-eslint/visitor-keys": 8.35.0
      eslint: 9.30.0
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/parser@8.35.0(eslint@9.30.0)(typescript@5.8.3)":
    dependencies:
      "@typescript-eslint/scope-manager": 8.35.0
      "@typescript-eslint/types": 8.35.0
      "@typescript-eslint/typescript-estree": 8.35.0(typescript@5.8.3)
      "@typescript-eslint/visitor-keys": 8.35.0
      debug: 4.4.1
      eslint: 9.30.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/project-service@8.35.0(typescript@5.8.3)":
    dependencies:
      "@typescript-eslint/tsconfig-utils": 8.35.0(typescript@5.8.3)
      "@typescript-eslint/types": 8.35.0
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/scope-manager@8.35.0":
    dependencies:
      "@typescript-eslint/types": 8.35.0
      "@typescript-eslint/visitor-keys": 8.35.0

  "@typescript-eslint/tsconfig-utils@8.35.0(typescript@5.8.3)":
    dependencies:
      typescript: 5.8.3

  "@typescript-eslint/type-utils@8.35.0(eslint@9.30.0)(typescript@5.8.3)":
    dependencies:
      "@typescript-eslint/typescript-estree": 8.35.0(typescript@5.8.3)
      "@typescript-eslint/utils": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.30.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/types@8.35.0": {}

  "@typescript-eslint/typescript-estree@8.35.0(typescript@5.8.3)":
    dependencies:
      "@typescript-eslint/project-service": 8.35.0(typescript@5.8.3)
      "@typescript-eslint/tsconfig-utils": 8.35.0(typescript@5.8.3)
      "@typescript-eslint/types": 8.35.0
      "@typescript-eslint/visitor-keys": 8.35.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/utils@8.35.0(eslint@9.30.0)(typescript@5.8.3)":
    dependencies:
      "@eslint-community/eslint-utils": 4.7.0(eslint@9.30.0)
      "@typescript-eslint/scope-manager": 8.35.0
      "@typescript-eslint/types": 8.35.0
      "@typescript-eslint/typescript-estree": 8.35.0(typescript@5.8.3)
      eslint: 9.30.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/visitor-keys@8.35.0":
    dependencies:
      "@typescript-eslint/types": 8.35.0
      eslint-visitor-keys: 4.2.1

  "@zip.js/zip.js@2.7.62": {}

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  are-docs-informative@0.0.2: {}

  argparse@2.0.1: {}

  async@3.2.6: {}

  balanced-match@1.0.2: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browser-or-node@3.0.0: {}

  buffer-from@1.1.2: {}

  callsites@3.1.0: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  chardet@0.7.0: {}

  chownr@3.0.0: {}

  cli-width@4.1.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  commander@12.1.0: {}

  commander@13.1.0: {}

  commander@2.20.3: {}

  comment-parser@1.4.1: {}

  commondir@1.0.1: {}

  concat-map@0.0.1: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  deep-eql@5.0.2: {}

  deep-is@0.1.4: {}

  deepcopy-esm@2.1.1: {}

  deepmerge@4.3.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  emoji-regex@8.0.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-plugin-jsdoc@50.8.0(eslint@9.30.0):
    dependencies:
      "@es-joy/jsdoccomment": 0.50.2
      are-docs-informative: 0.0.2
      comment-parser: 1.4.1
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint: 9.30.0
      espree: 10.4.0
      esquery: 1.6.0
      parse-imports-exports: 0.2.4
      semver: 7.7.2
      spdx-expression-parse: 4.0.0
    transitivePeerDependencies:
      - supports-color

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.30.0:
    dependencies:
      "@eslint-community/eslint-utils": 4.7.0(eslint@9.30.0)
      "@eslint-community/regexpp": 4.12.1
      "@eslint/config-array": 0.21.0
      "@eslint/config-helpers": 0.3.0
      "@eslint/core": 0.14.0
      "@eslint/eslintrc": 3.3.1
      "@eslint/js": 9.30.0
      "@eslint/plugin-kit": 0.3.3
      "@humanfs/node": 0.16.6
      "@humanwhocodes/module-importer": 1.0.1
      "@humanwhocodes/retry": 0.4.3
      "@types/estree": 1.0.8
      "@types/json-schema": 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  expect-type@1.2.1: {}

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      "@nodelib/fs.walk": 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.6(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-process@1.4.10:
    dependencies:
      chalk: 4.1.2
      commander: 12.1.0
      loglevel: 1.9.2

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  globals@11.12.0: {}

  globals@14.0.0: {}

  graphemer@1.4.0: {}

  has-flag@4.0.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inquirer@12.6.3(@types/node@20.15.0):
    dependencies:
      "@inquirer/core": 10.1.13(@types/node@20.15.0)
      "@inquirer/prompts": 7.5.3(@types/node@20.15.0)
      "@inquirer/type": 3.0.7(@types/node@20.15.0)
      ansi-escapes: 4.3.2
      mute-stream: 2.0.0
      run-async: 3.0.0
      rxjs: 7.8.2
    optionalDependencies:
      "@types/node": 20.15.0

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-interactive@2.0.0: {}

  is-module@1.0.0: {}

  is-number@7.0.0: {}

  is-reference@1.2.1:
    dependencies:
      "@types/estree": 1.0.8

  is-unicode-supported@2.1.0: {}

  isexe@2.0.0: {}

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  javascript-natural-sort@0.7.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdoc-type-pratt-parser@4.1.0: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  log-symbols@7.0.1:
    dependencies:
      is-unicode-supported: 2.1.0
      yoctocolors: 2.1.1

  loglevel@1.9.2: {}

  luxon@3.6.1: {}

  magic-string@0.30.17:
    dependencies:
      "@jridgewell/sourcemap-codec": 1.5.0

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mkdirp@3.0.1: {}

  ms@2.1.3: {}

  mute-stream@2.0.0: {}

  natural-compare@1.4.0: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  os-tmpdir@1.0.2: {}

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-imports-exports@0.2.4:
    dependencies:
      parse-statements: 1.0.11

  parse-statements@1.0.11: {}

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  prelude-ls@1.2.1: {}

  prettier-plugin-multiline-arrays@4.0.3(prettier@3.6.2):
    dependencies:
      "@augment-vir/common": 31.26.0
      prettier: 3.6.2
      proxy-vir: 2.0.1

  prettier@3.6.2: {}

  proxy-vir@2.0.1:
    dependencies:
      "@augment-vir/assert": 31.26.0
      "@augment-vir/common": 31.26.0

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  rage-edit@1.2.0: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  require-from-string@2.0.2: {}

  resolve-from@4.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.1.0: {}

  rollup@4.44.1:
    dependencies:
      "@types/estree": 1.0.8
    optionalDependencies:
      "@rollup/rollup-android-arm-eabi": 4.44.1
      "@rollup/rollup-android-arm64": 4.44.1
      "@rollup/rollup-darwin-arm64": 4.44.1
      "@rollup/rollup-darwin-x64": 4.44.1
      "@rollup/rollup-freebsd-arm64": 4.44.1
      "@rollup/rollup-freebsd-x64": 4.44.1
      "@rollup/rollup-linux-arm-gnueabihf": 4.44.1
      "@rollup/rollup-linux-arm-musleabihf": 4.44.1
      "@rollup/rollup-linux-arm64-gnu": 4.44.1
      "@rollup/rollup-linux-arm64-musl": 4.44.1
      "@rollup/rollup-linux-loongarch64-gnu": 4.44.1
      "@rollup/rollup-linux-powerpc64le-gnu": 4.44.1
      "@rollup/rollup-linux-riscv64-gnu": 4.44.1
      "@rollup/rollup-linux-riscv64-musl": 4.44.1
      "@rollup/rollup-linux-s390x-gnu": 4.44.1
      "@rollup/rollup-linux-x64-gnu": 4.44.1
      "@rollup/rollup-linux-x64-musl": 4.44.1
      "@rollup/rollup-win32-arm64-msvc": 4.44.1
      "@rollup/rollup-win32-ia32-msvc": 4.44.1
      "@rollup/rollup-win32-x64-msvc": 4.44.1
      fsevents: 2.3.3

  run-async@3.0.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  semver@7.7.2: {}

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  signal-exit@4.1.0: {}

  smob@1.5.0: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@4.0.0:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-json-comments@3.1.1: {}

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  tar@7.4.3:
    dependencies:
      "@isaacs/fs-minipass": 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  terser@5.43.1:
    dependencies:
      "@jridgewell/source-map": 0.3.6
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  tslib@2.8.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.21.3: {}

  type-fest@4.41.0: {}

  typed-event-target@4.1.0:
    dependencies:
      "@augment-vir/assert": 31.26.0
      "@augment-vir/common": 31.26.0
      "@augment-vir/core": 31.26.0

  typescript-eslint@8.35.0(eslint@9.30.0)(typescript@5.8.3):
    dependencies:
      "@typescript-eslint/eslint-plugin": 8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.30.0)(typescript@5.8.3))(eslint@9.30.0)(typescript@5.8.3)
      "@typescript-eslint/parser": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      "@typescript-eslint/utils": 8.35.0(eslint@9.30.0)(typescript@5.8.3)
      eslint: 9.30.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  undici-types@6.13.0: {}

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  ws@8.18.3: {}

  yallist@5.0.0: {}

  yocto-queue@0.1.0: {}

  yoctocolors-cjs@2.1.2: {}

  yoctocolors@2.1.1: {}
