# MSPA Stream Deck Plugin Development Guide

## Project Overview

You are a professional developer creating a Stream Deck plugin called "mspa" with author name "<PERSON><PERSON><PERSON>". This plugin will integrate with the MSPA API to provide Stream Deck functionality.

## Plugin Specifications

### Basic Information

- **Plugin Name**: mspa
- **Author**: Trystan34
- **Bundle ID**: com.trystan34.mspa

### Required Input Fields

The plugin should include a Property Inspector with the following input fields for MSPA login details:

1. **Account <PERSON><PERSON>** (text input)
    - Type: email
    - Required: true
    - Placeholder: "Enter your MSPA account email"

2. **Password** (password input, stored as MD5 hash)
    - Type: password
    - Required: true
    - Note: Convert to MD5 hash before storage
    - Placeholder: "Enter your password"

3. **Device ID** (text input)
    - Type: text
    - Required: true
    - Placeholder: "Enter your device ID"

4. **Product ID** (text input with reference link)
    - Type: text
    - Required: true
    - Placeholder: "Enter your product ID"
    - Include link to: https://github.com/elgatosf/streamdeck

### Development Setup

#### Prerequisites

- Node.js v20 (use nvm for version management)
- Stream Deck CLI: `npm install -g @elgato/cli@latest`

#### Project Creation

```bash
streamdeck create
```

#### Key Files Structure

```
mspa.sdPlugin/
├── manifest.json
├── src/
│   ├── actions/
│   └── property-inspector/
├── package.json
└── README.md
```

### MSPA API Analysis

Based on the mspa-api.js file, the MSPA API provides:

#### Available Data Fields:

- `temperature_setting`: Target temperature (API returns doubled Celsius values)
- `water_temperature`: Current water temperature (API returns doubled Celsius values)
- Various mode states (to be discovered from API responses)

#### Temperature Handling:

- API stores temperatures as doubled Celsius values (e.g., 80 = 40°C)
- API includes conversion logic to/from Fahrenheit for display
- Plugin needs Celsius/Fahrenheit toggle in settings

#### API Endpoints:

- Authentication: `/api/enduser/get_token/`
- Status: `/api/device/thing_shadow/`
- Commands: `/api/device/command`

### Implementation Guidelines

#### 1. Credential Storage

- Use Stream Deck's built-in settings persistence
- Store credentials securely using the plugin's settings API
- Never store plain text passwords - convert to MD5 hash
- Include temperature unit preference (Celsius/Fahrenheit)
- Example implementation:

```typescript
// Store settings
await this.plugin.setSettings({
    email: email,
    passwordHash: md5(password),
    deviceId: deviceId,
    productId: productId,
    temperatureUnit: "fahrenheit", // or 'celsius'
});
```

#### 2. Property Inspector (UI Configuration)

Create HTML form in property inspector:

```html
<div class="sdpi-item">
    <div class="sdpi-item-label">Account Email</div>
    <input class="sdpi-item-value" type="email" id="email" placeholder="Enter your MSPA account email" />
</div>

<div class="sdpi-item">
    <div class="sdpi-item-label">Password</div>
    <input class="sdpi-item-value" type="password" id="password" placeholder="Enter your password" />
</div>

<div class="sdpi-item">
    <div class="sdpi-item-label">Device ID</div>
    <input class="sdpi-item-value" type="text" id="deviceId" placeholder="Enter your device ID" />
</div>

<div class="sdpi-item">
    <div class="sdpi-item-label">Product ID</div>
    <input class="sdpi-item-value" type="text" id="productId" placeholder="Enter your product ID" />
    <div class="sdpi-item-info">
        <a href="https://github.com/elgatosf/streamdeck" target="_blank">Stream Deck SDK Reference</a>
    </div>
</div>

<div class="sdpi-item">
    <div class="sdpi-item-label">Temperature Unit</div>
    <select class="sdpi-item-value" id="temperatureUnit">
        <option value="fahrenheit">Fahrenheit (°F)</option>
        <option value="celsius">Celsius (°C)</option>
    </select>
</div>
```

#### 3. Dynamic Icon Design (72x72px Canvas)

Stream Deck icons must be 72x72 pixels (144x144 for high DPI). The plugin should display:

- **Target Temperature**: Large text showing set temperature
- **Current Temperature**: Smaller text showing actual temperature
- **Mode Indicators**: Icons or colors showing current operational modes
- **Status**: Visual indicators for connection status

#### 4. MSPA API Integration

Reference the provided MSPA API code for:

- Authentication handling with MD5 signatures
- Temperature conversion (API uses doubled Celsius values)
- Device status polling
- Command sending
- Token management

#### 5. Action Implementation

```typescript
@action({ UUID: "com.trystan34.mspa.main-action" })
export class MSPAAction extends SingletonAction {
    private updateInterval?: ReturnType<typeof setInterval>;

    async onWillAppear(ev: WillAppearEvent) {
        // Start periodic updates when action appears
        this.updateInterval = setInterval(async () => {
            await this.updateStatus(ev.action);
        }, 30000); // Update every 30 seconds

        // Initial update
        await this.updateStatus(ev.action);
    }

    async onWillDisappear() {
        // Clean up interval when action disappears
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }

    async onKeyDown(ev: KeyDownEvent) {
        // Toggle hot tub power or cycle through modes
        await this.performMSPAAction(ev.action);
    }

    private async updateStatus(action: Action) {
        const settings = await action.getSettings();

        if (!this.validateCredentials(settings)) {
            await this.drawConfigIcon(action);
            return;
        }

        try {
            const status = await this.getMSPAStatus(settings);
            await this.drawTemperatureIcon(action, status, settings.temperatureUnit);
        } catch (error) {
            await this.drawErrorIcon(action);
        }
    }

    private async drawTemperatureIcon(action: Action, status: any, unit: string) {
        const canvas = document.createElement("canvas");
        canvas.width = 144; // High DPI
        canvas.height = 144;
        const ctx = canvas.getContext("2d")!;

        // Background
        ctx.fillStyle = "#1a1a1a";
        ctx.fillRect(0, 0, 144, 144);

        // Current temperature (large, center)
        const currentTemp = this.convertTemperature(status.water_temperature, unit);
        ctx.fillStyle = "#00ff00";
        ctx.font = "bold 36px Arial";
        ctx.textAlign = "center";
        ctx.fillText(`${currentTemp}°`, 72, 70);

        // Target temperature (smaller, bottom)
        const targetTemp = this.convertTemperature(status.temperature_setting, unit);
        ctx.fillStyle = "#ffffff";
        ctx.font = "16px Arial";
        ctx.fillText(`Target: ${targetTemp}°${unit.charAt(0).toUpperCase()}`, 72, 95);

        // Status indicator (top)
        ctx.fillStyle = status.power ? "#00ff00" : "#ff0000";
        ctx.font = "12px Arial";
        ctx.fillText(status.power ? "ON" : "OFF", 72, 20);

        const dataURL = canvas.toDataURL();
        await action.setImage(dataURL);
    }

    private convertTemperature(apiTemp: number, unit: string): number {
        const celsius = apiTemp / 2; // API returns doubled Celsius
        return unit === "fahrenheit" ? Math.round(((celsius * 9) / 5 + 32) * 10) / 10 : Math.round(celsius * 10) / 10;
    }

    private validateCredentials(settings: any): boolean {
        return settings.email && settings.passwordHash && settings.deviceId && settings.productId;
    }
}
```

### Build Commands

- Development: `npm run watch`
- Production: `npm run build`
- Debug: Configure in manifest.json for Node.js debugging

### Security Best Practices

1. Never log or expose user credentials
2. Use HTTPS for all API communications
3. Validate all user inputs
4. Hash passwords before storage
5. Use Stream Deck's built-in settings encryption where available

### Resources

- [Stream Deck SDK Documentation](https://docs.elgato.com/sdk)
- [Stream Deck GitHub Repository](https://github.com/elgatosf/streamdeck)
- [Plugin Samples](https://github.com/elgatosf/streamdeck-plugin-samples)
- [Marketplace Makers Discord](https://discord.gg/elgato) for technical support

### Next Steps

1. Project has already been initialized with Stream Deck CLI
2. Set up TypeScript configuration
3. Implement property inspector UI
4. Create main action class
5. Integrate MSPA API functionality
6. Test credential storage and retrieval
7. Implement error handling and validation
